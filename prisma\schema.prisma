// LaundrySense Database Schema
// Contextual Intelligence Laundry Management System
// Phase 1: Foundation & Core Engine

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// CORE ENTITIES
// ============================================================================

model Customer {
  id                        String   @id @default(cuid())
  name                      String
  phone_number              String   @unique
  email                     String?  @unique
  address                   String?
  registration_date         DateTime @default(now())
  last_transaction_date     DateTime?

  // Behavioral Intelligence Scores (0.0 - 1.0)
  behavior_frequency_score  Float    @default(0.0) // How often they use service
  behavior_preference_score Float    @default(0.0) // Service type preferences
  behavior_seasonal_bias    Float    @default(0.0) // Seasonal usage patterns

  // Relations
  transactions              Transaction[]
  customer_pattern          CustomerPattern?

  // Indexes for performance
  @@index([phone_number])
  @@index([email])
  @@index([last_transaction_date])
  @@index([behavior_frequency_score])
  @@map("customers")
}

model MaterialInventory {
  id                        String   @id @default(cuid())
  material_name             String   @unique
  current_stock_unit        Float
  unit_of_measure           String   // 'ml', 'gram', 'piece', 'liter'
  last_restock_date         DateTime @default(now())
  usage_rate_per_transaction Float   @default(0.0) // Average usage per transaction
  usage_rate_per_kg         Float   @default(0.0) // Average usage per kg
  minimum_stock_threshold   Float   @default(0.0) // Alert threshold
  cost_per_unit             Float   @default(0.0) // Cost tracking
  supplier_info             String?

  // Material Categories
  category                  MaterialCategory @default(DETERGENT)

  // Relations
  transaction_materials     TransactionMaterial[]
  usage_pattern             MaterialUsagePattern?

  // Indexes for performance
  @@index([material_name])
  @@index([category])
  @@index([current_stock_unit])
  @@map("materials_inventory")
}

model Transaction {
  id                        String   @id @default(cuid())
  customer_id               String
  service_type              ServiceType
  weight_kg                 Float
  price                     Float
  transaction_date          DateTime @default(now())
  status                    TransactionStatus @default(PENDING)

  // Contextual Intelligence Data
  context_weather           WeatherContext @default(SUNNY)
  context_day_type          DayType @default(WEEKDAY)
  context_seasonal_factor   Float   @default(0.5) // 0.0 = low season, 1.0 = peak season
  context_time_of_day       TimeOfDay @default(MORNING)

  // Additional Business Context
  pickup_date               DateTime?
  delivery_date             DateTime?
  special_instructions      String?
  discount_applied          Float   @default(0.0)

  // Relations
  customer                  Customer @relation(fields: [customer_id], references: [id], onDelete: Cascade)
  transaction_materials     TransactionMaterial[]

  // Indexes for performance optimization
  @@index([customer_id])
  @@index([transaction_date])
  @@index([status])
  @@index([service_type])
  @@index([context_weather])
  @@index([context_day_type])
  @@index([context_seasonal_factor])
  @@map("transactions")
}

// Junction table for materials used in transactions
model TransactionMaterial {
  id              String   @id @default(cuid())
  transaction_id  String
  material_id     String
  quantity_used   Float
  cost_at_time    Float    // Cost when transaction occurred

  // Relations
  transaction     Transaction @relation(fields: [transaction_id], references: [id], onDelete: Cascade)
  material        MaterialInventory @relation(fields: [material_id], references: [id], onDelete: Cascade)

  // Composite unique constraint
  @@unique([transaction_id, material_id])
  @@index([transaction_id])
  @@index([material_id])
  @@map("transaction_materials")
}

// ============================================================================
// ENUMS FOR CONTEXTUAL INTELLIGENCE
// ============================================================================

enum ServiceType {
  CUCI_KERING      // Wash & Dry
  CUCI_SETRIKA     // Wash & Iron
  SETRIKA_SAJA     // Iron Only
  CUCI_SAJA        // Wash Only
  DRY_CLEAN        // Dry Cleaning
  SEPATU           // Shoe Cleaning
  KARPET           // Carpet Cleaning
  SELIMUT          // Blanket/Comforter
}

enum TransactionStatus {
  PENDING          // Order received
  IN_PROGRESS      // Being processed
  READY            // Ready for pickup
  COMPLETED        // Picked up by customer
  CANCELLED        // Cancelled order
}

enum WeatherContext {
  SUNNY
  RAINY
  CLOUDY
  STORMY
  HOT
  HUMID
}

enum DayType {
  WEEKDAY
  WEEKEND
  HOLIDAY
  SPECIAL_EVENT    // Local events that affect business
}

enum TimeOfDay {
  EARLY_MORNING    // 6-9 AM
  MORNING          // 9-12 PM
  AFTERNOON        // 12-5 PM
  EVENING          // 5-8 PM
  NIGHT            // 8-10 PM
}

enum MaterialCategory {
  DETERGENT        // Liquid/powder detergent
  FABRIC_SOFTENER  // Fabric softener/conditioner
  BLEACH           // Bleaching agents
  STAIN_REMOVER    // Stain removal products
  PACKAGING        // Plastic bags, hangers
  EQUIPMENT        // Machine maintenance items
  FRAGRANCE        // Perfumes, fresheners
  OTHER            // Miscellaneous items
}

// ============================================================================
// PATTERN ANALYSIS TABLES
// ============================================================================

model CustomerPattern {
  id                        String   @id @default(cuid())
  customer_id               String   @unique
  calculated_at             DateTime @default(now())

  // Frequency Analysis
  calculated_frequency      Float    // Transactions per month
  frequency_trend           String   // 'increasing', 'decreasing', 'stable'
  last_transaction_days_ago Int      // Days since last transaction

  // Preference Analysis
  preferred_service_type    ServiceType
  preferred_service_confidence Float @default(0.0) // 0.0-1.0
  average_weight_kg         Float
  average_spending          Float

  // Seasonal Analysis
  seasonal_activity_json    Json     // Monthly activity patterns
  peak_months              String   // Comma-separated peak months
  low_months               String   // Comma-separated low months
  seasonal_variance        Float    // Measure of seasonal variation

  // Behavioral Scores
  loyalty_score            Float    @default(0.0) // 0.0-1.0
  value_score              Float    @default(0.0) // 0.0-1.0
  predictability_score     Float    @default(0.0) // 0.0-1.0

  // Confidence Metrics
  confidence_score         Float    @default(0.0) // Overall pattern confidence
  data_points_count        Int      @default(0)   // Number of transactions analyzed
  analysis_period_days     Int      @default(0)   // Days of data analyzed

  // Relations
  customer                 Customer @relation(fields: [customer_id], references: [id], onDelete: Cascade)

  @@index([customer_id])
  @@index([calculated_at])
  @@index([confidence_score])
  @@map("customer_patterns")
}

model MaterialUsagePattern {
  id                        String   @id @default(cuid())
  material_id               String   @unique
  calculated_at             DateTime @default(now())

  // Consumption Analysis
  average_consumption_rate  Float    // Units per day
  consumption_trend         String   // 'increasing', 'decreasing', 'stable'
  peak_usage_day_type       DayType  // When usage is highest
  peak_usage_time           TimeOfDay // When usage is highest

  // Seasonal Analysis
  seasonal_adjustment_json  Json     // Monthly consumption patterns
  peak_consumption_months   String   // Comma-separated peak months
  low_consumption_months    String   // Comma-separated low months
  seasonal_multiplier       Float    @default(1.0) // Peak vs low season ratio

  // Efficiency Metrics
  cost_efficiency_score     Float    @default(0.0) // Cost per transaction efficiency
  usage_efficiency_score    Float    @default(0.0) // Usage per kg efficiency
  waste_indicator           Float    @default(0.0) // Potential waste measure

  // Stock Predictions
  predicted_days_to_empty   Int      @default(0)   // Days until stock runs out
  reorder_recommendation    Boolean  @default(false)
  optimal_stock_level       Float    @default(0.0) // Recommended stock level

  // Confidence Metrics
  confidence_score          Float    @default(0.0) // Overall pattern confidence
  data_points_count         Int      @default(0)   // Number of usage records
  analysis_period_days      Int      @default(0)   // Days of data analyzed

  // Relations
  material                  MaterialInventory @relation(fields: [material_id], references: [id], onDelete: Cascade)

  @@index([material_id])
  @@index([calculated_at])
  @@index([reorder_recommendation])
  @@map("material_usage_patterns")
}

model RevenueTrend {
  id                        String   @id @default(cuid())
  date                      DateTime @unique

  // Daily Metrics
  daily_revenue             Float    @default(0.0)
  daily_transaction_count   Int      @default(0)
  daily_avg_transaction     Float    @default(0.0)
  daily_weight_total        Float    @default(0.0)

  // Weekly Context (calculated for the week this date belongs to)
  weekly_avg_revenue        Float    @default(0.0)
  weekly_transaction_count  Int      @default(0)
  week_day_rank             Int      @default(0) // 1-7, where this day ranks in the week

  // Monthly Context (calculated for the month this date belongs to)
  monthly_avg_revenue       Float    @default(0.0)
  monthly_transaction_count Int      @default(0)
  month_day_rank            Int      @default(0) // Where this day ranks in the month

  // Trend Analysis
  revenue_trend             String   // 'peak', 'high', 'normal', 'low', 'valley'
  peak_status               String   // 'daily_peak', 'weekly_peak', 'monthly_peak', 'normal'
  growth_rate               Float    @default(0.0) // Compared to previous period

  // Contextual Data
  day_type                  DayType
  weather_context           WeatherContext
  seasonal_factor           Float    @default(0.5)

  // Confidence Metrics
  confidence_score          Float    @default(0.0) // Data reliability score
  data_completeness         Float    @default(0.0) // Percentage of expected data

  @@index([date])
  @@index([peak_status])
  @@index([revenue_trend])
  @@index([day_type])
  @@map("revenue_trends")
}

model PatternCalculationLog {
  id                        String   @id @default(cuid())
  calculation_type          String   // 'customer_patterns', 'material_patterns', 'revenue_trends'
  started_at                DateTime @default(now())
  completed_at              DateTime?
  status                    String   // 'running', 'completed', 'failed'
  records_processed         Int      @default(0)
  records_updated           Int      @default(0)
  error_message             String?
  execution_time_ms         Int      @default(0)

  @@index([calculation_type])
  @@index([started_at])
  @@index([status])
  @@map("pattern_calculation_logs")
}
