"use client";

import React, { useState, useEffect } from 'react';
import { useCustomers } from '@/hooks/useApi';
import DataTable from '@/components/ui/DataTable';
import Modal from '@/components/ui/Modal';
import FormField from '@/components/ui/FormField';
import { User, Phone, Mail, MapPin, Calendar } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  address?: string;
  registration_date: string;
  last_transaction_date?: string;
  behavior_frequency_score: number;
  behavior_preference_score: number;
  behavior_seasonal_bias: number;
  _count?: {
    transactions: number;
  };
}

interface CustomerFormData {
  name: string;
  phone_number: string;
  email: string;
  address: string;
  behavior_frequency_score: number;
  behavior_preference_score: number;
  behavior_seasonal_bias: number;
}

const CustomersPage: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    phone_number: '',
    email: '',
    address: '',
    behavior_frequency_score: 0,
    behavior_preference_score: 0,
    behavior_seasonal_bias: 0,
  });
  const [formErrors, setFormErrors] = useState<Partial<CustomerFormData>>({});

  const {
    loading,
    error,
    getCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
  } = useCustomers();

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    const response = await getCustomers();
    if (response.success && response.data) {
      setCustomers(response.data);
    }
  };

  const validateForm = (): boolean => {
    const errors: Partial<CustomerFormData> = {};

    if (!formData.name.trim()) {
      errors.name = 'Nama wajib diisi';
    }

    if (!formData.phone_number.trim()) {
      errors.phone_number = 'Nomor telepon wajib diisi';
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone_number)) {
      errors.phone_number = 'Format nomor telepon tidak valid';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Format email tidak valid';
    }

    if (formData.behavior_frequency_score < 0 || formData.behavior_frequency_score > 1) {
      errors.behavior_frequency_score = 'Skor harus antara 0 dan 1';
    }

    if (formData.behavior_preference_score < 0 || formData.behavior_preference_score > 1) {
      errors.behavior_preference_score = 'Skor harus antara 0 dan 1';
    }

    if (formData.behavior_seasonal_bias < 0 || formData.behavior_seasonal_bias > 1) {
      errors.behavior_seasonal_bias = 'Skor harus antara 0 dan 1';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const submitData = {
      ...formData,
      email: formData.email || null,
      address: formData.address || null,
    };

    let response;
    if (editingCustomer) {
      response = await updateCustomer(editingCustomer.id, submitData);
    } else {
      response = await createCustomer(submitData);
    }

    if (response.success) {
      setIsModalOpen(false);
      resetForm();
      loadCustomers();
    }
  };

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone_number: customer.phone_number,
      email: customer.email || '',
      address: customer.address || '',
      behavior_frequency_score: customer.behavior_frequency_score,
      behavior_preference_score: customer.behavior_preference_score,
      behavior_seasonal_bias: customer.behavior_seasonal_bias,
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (customer: Customer) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus pelanggan ${customer.name}?`)) {
      const response = await deleteCustomer(customer.id);
      if (response.success) {
        loadCustomers();
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      phone_number: '',
      email: '',
      address: '',
      behavior_frequency_score: 0,
      behavior_preference_score: 0,
      behavior_seasonal_bias: 0,
    });
    setFormErrors({});
    setEditingCustomer(null);
  };

  const handleCreate = () => {
    resetForm();
    setIsModalOpen(true);
  };

  const columns = [
    {
      key: 'name',
      label: 'Nama',
      sortable: true,
      render: (value: string, row: Customer) => (
        <div className="flex items-center gap-2">
          <User size={16} className="text-gray-400" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'phone_number',
      label: 'Telepon',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Phone size={16} className="text-gray-400" />
          <span>{value}</span>
        </div>
      ),
    },
    {
      key: 'email',
      label: 'Email',
      render: (value: string) => (
        value ? (
          <div className="flex items-center gap-2">
            <Mail size={16} className="text-gray-400" />
            <span>{value}</span>
          </div>
        ) : (
          <span className="text-gray-400">-</span>
        )
      ),
    },
    {
      key: '_count',
      label: 'Transaksi',
      render: (value: any) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
          {value?.transactions || 0}
        </span>
      ),
    },
    {
      key: 'behavior_frequency_score',
      label: 'Skor Frekuensi',
      render: (value: number) => (
        <span className="text-sm">{(value * 100).toFixed(1)}%</span>
      ),
    },
    {
      key: 'registration_date',
      label: 'Terdaftar',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Calendar size={16} className="text-gray-400" />
          <span className="text-sm">{new Date(value).toLocaleDateString('id-ID')}</span>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Pelanggan</h1>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <DataTable
        title="Daftar Pelanggan"
        columns={columns}
        data={customers}
        loading={loading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onCreate={handleCreate}
        emptyMessage="Belum ada pelanggan terdaftar"
      />

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingCustomer ? 'Edit Pelanggan' : 'Tambah Pelanggan'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Nama"
              name="name"
              value={formData.name}
              onChange={(value) => setFormData({ ...formData, name: value as string })}
              error={formErrors.name}
              required
            />

            <FormField
              label="Nomor Telepon"
              name="phone_number"
              type="tel"
              value={formData.phone_number}
              onChange={(value) => setFormData({ ...formData, phone_number: value as string })}
              error={formErrors.phone_number}
              required
            />

            <FormField
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={(value) => setFormData({ ...formData, email: value as string })}
              error={formErrors.email}
            />

            <FormField
              label="Alamat"
              name="address"
              type="textarea"
              value={formData.address}
              onChange={(value) => setFormData({ ...formData, address: value as string })}
              rows={2}
            />

            <FormField
              label="Skor Frekuensi"
              name="behavior_frequency_score"
              type="number"
              value={formData.behavior_frequency_score}
              onChange={(value) => setFormData({ ...formData, behavior_frequency_score: value as number })}
              error={formErrors.behavior_frequency_score}
              min={0}
              max={1}
              step={0.1}
            />

            <FormField
              label="Skor Preferensi"
              name="behavior_preference_score"
              type="number"
              value={formData.behavior_preference_score}
              onChange={(value) => setFormData({ ...formData, behavior_preference_score: value as number })}
              error={formErrors.behavior_preference_score}
              min={0}
              max={1}
              step={0.1}
            />
          </div>

          <FormField
            label="Bias Musiman"
            name="behavior_seasonal_bias"
            type="number"
            value={formData.behavior_seasonal_bias}
            onChange={(value) => setFormData({ ...formData, behavior_seasonal_bias: value as number })}
            error={formErrors.behavior_seasonal_bias}
            min={0}
            max={1}
            step={0.1}
          />

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Menyimpan...' : editingCustomer ? 'Update' : 'Simpan'}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default CustomersPage;
