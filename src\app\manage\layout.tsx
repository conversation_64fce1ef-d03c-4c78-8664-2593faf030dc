import { ReactNode } from 'react';
import Link from 'next/link';

export default function ManageLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Manajemen LaundrySense</h1>
            <nav className="space-x-4">
              <Link href="/manage/transactions" className="text-gray-600 hover:text-gray-900">Transaksi</Link>
              <Link href="/manage/customers" className="text-gray-600 hover:text-gray-900">Pelanggan</Link>
              <Link href="/manage/inventory" className="text-gray-600 hover:text-gray-900">Inventaris</Link>
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 font-semibold">Kembali ke Dashboard</Link>
            </nav>
          </div>
        </div>
      </header>
      <main>
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>
    </div>
  );
}
