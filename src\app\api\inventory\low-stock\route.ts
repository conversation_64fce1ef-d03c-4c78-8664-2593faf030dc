import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * API endpoint to get materials with low stock levels using the type-safe Prisma Client.
 * This approach uses modern Prisma features to compare two columns directly.
 */
export async function GET() {
  try {
    const lowStockItems = await prisma.materialInventory.findMany({
      where: {
        // This condition uses Prisma's field reference feature to compare two columns.
        // It finds items where the current stock is less than or equal to the minimum threshold.
        current_stock_unit: {
          lte: prisma.materialInventory.fields.minimum_stock_threshold,
        },
      },
      select: {
        id: true,
        material_name: true,
        current_stock_unit: true,
        minimum_stock_threshold: true,
        unit_of_measure: true,
      },
    });

    // Map the database fields to the frontend's expected camelCase format.
    const formattedItems = lowStockItems.map((item) => ({
      id: item.id,
      name: item.material_name,
      currentStock: item.current_stock_unit,
      minStock: item.minimum_stock_threshold,
      unit: item.unit_of_measure,
    }));

    return NextResponse.json(formattedItems);
  } catch (error) {
    console.error('--- DETAILED API ERROR in /api/inventory/low-stock ---');
    console.error('A critical error occurred while fetching data from the database:');
    console.error(error);
    console.error('--- END DETAILED API ERROR ---');

    return NextResponse.json(
      { message: 'Internal Server Error. Please check the server console for detailed logs.' },
      { status: 500 }
    );
  }
}
