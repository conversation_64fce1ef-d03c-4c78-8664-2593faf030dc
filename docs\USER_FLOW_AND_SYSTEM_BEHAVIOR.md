# Dokumentasi Alur Pengguna dan Perilaku Sistem

## 1. Filosofi Inti: Dasbor sebagai Asisten Proaktif

Tidak seperti dasbor tradisional yang hanya menyajikan data mentah dan mengharuskan pengguna untuk menganalisisnya, LaundrySense dirancang dengan filosofi **asisten proaktif**. Sistem ini bertujuan untuk mengurangi beban kognitif pengguna dengan melakukan analisis di latar belakang dan menyajikan wawasan yang paling relevan pada waktu yang tepat.

Pengguna utama aplikasi ini bukanlah operator entri data, melainkan **pemilik bisnis atau manajer** yang tujuannya adalah membuat keputusan strategis. <PERSON>h karena itu, alur pengguna utama berpusat pada **konsumsi informasi yang cerdas**, bukan pada interaksi yang rumit.

---

## 2. Persona Pengguna: Pemilik Bisnis yang Sibuk

*   **Siapa Dia?**: Seorang pemilik usaha laundry yang mengelola operasi sehari-hari, staf, inventaris, dan keuangan.
*   **Tujuannya**: 
    *   Meningkatkan efisiensi operasional.
    *   Mengoptimalkan penggunaan bahan baku dan inventaris.
    *   Memahami perilaku pelanggan untuk meningkatkan loyalitas.
    *   Mengidentifikasi tren pendapatan dan peluang pertumbuhan.
*   **Kebutuhannya**: Dia tidak punya waktu untuk menelusuri spreadsheet atau laporan yang rumit. Dia membutuhkan informasi yang ringkas, relevan, dan dapat ditindaklanjuti dengan cepat.

---

## 3. Alur Pengguna Utama: Pengalaman Pasif Real-time

Alur pengguna utama di LaundrySense bersifat unik karena sebagian besar **pasif**. Pengguna tidak perlu melakukan banyak hal untuk mendapatkan nilai; sistem yang bekerja untuknya. Berikut adalah skenario langkah demi langkah:

1.  **Pengguna Mengunjungi Dasbor**: Pengguna membuka aplikasi dan menavigasi ke halaman dasbor.

2.  **Sistem Mendeteksi Konteks**: Secara instan, frontend membuka koneksi WebSocket ke server backend.

3.  **Pembaruan Real-time Dimulai**: Server backend, yang terus-menerus menganalisis data, segera mengirimkan `DashboardState` terbaru ke frontend. Pembaruan ini terjadi secara otomatis setiap 15 detik atau setiap kali ada perubahan signifikan.

4.  **UI Beradaptasi secara Dinamis**: Dasbor pengguna langsung diperbarui dengan informasi baru:
    *   **Konteks Saat Ini**: Kartu di bagian atas menampilkan konteks saat ini (misalnya, "Pagi Hari Kerja yang Sibuk") dengan ikon yang sesuai.
    *   **Mode yang Direkomendasikan**: Sistem menyarankan mode fokus (misalnya, "Mode Efisiensi Operasional").
    *   **Wawasan Prioritas**: Daftar wawasan yang paling relevan dengan konteks saat ini ditampilkan. Setiap wawasan disajikan dalam format `InsightCard` yang mudah dicerna.

5.  **Pengguna Mengkonsumsi Informasi (Progressive Disclosure)**:
    *   **Tingkat Tinggi**: Pengguna mendapatkan gambaran umum tentang keadaan bisnis hanya dengan melihat konteks dan mode yang direkomendasikan.
    *   **Tingkat Menengah**: Pengguna memindai daftar judul wawasan prioritas untuk melihat apa yang paling penting saat ini.
    *   **Tingkat Detail**: Jika sebuah wawasan menarik perhatiannya (misalnya, "Peringatan Stok Rendah untuk Deterjen X"), pengguna dapat membaca deskripsi dan tindakan yang disarankan pada kartu tersebut.

6.  **Siklus Berulang**: Pengguna dapat membiarkan dasbor terbuka. Saat bisnis berjalan dan data baru masuk, dasbor akan terus memperbarui dirinya sendiri, memberikan aliran kesadaran situasional yang berkelanjutan.

---

## 4. Alur Data Internal Sistem (Di Balik Layar)

Pengalaman pengguna yang mulus di atas didukung oleh alur data yang canggih di backend. Berikut adalah perjalanannya:

1.  **Pemicu Berkala (`server.ts`)**: Setiap 15 detik, `setInterval` di server WebSocket memicu proses pembuatan konteks baru.

2.  **Simulasi Data (`context-simulation.ts`)**: Fungsi `generateRandomContextUpdate` dipanggil. Ini mensimulasikan pengumpulan data bisnis terbaru (transaksi, penggunaan material, dll.) dan menghasilkan objek `ContextDetectionInput` yang realistis.

3.  **Deteksi Konteks (`context-detector.ts`)**: Objek input diteruskan ke `ContextDetector`. Lapisan ini menganalisis data untuk menentukan konteks objektif (misalnya, waktu, hari, metrik bisnis).

4.  **Generasi Wawasan (`insight-generator.ts`)**: Hasil deteksi konteks kemudian diteruskan ke `InsightGenerator`. Lapisan ini menggunakan logika bisnis untuk menghasilkan `PriorityInsight` (wawasan dalam bentuk string) dan merekomendasikan `DashboardMode`.

5.  **Pemetaan untuk Frontend (`insight-mapper.ts`)**: Sebelum dikirim, wawasan berbentuk string dari backend dipetakan menjadi objek `Insight` yang kaya (dengan ikon, deskripsi, dll.) yang dibutuhkan oleh komponen UI frontend.

6.  **Siaran WebSocket (`server.ts`)**: Objek `DashboardState` yang lengkap dan telah dipetakan dibungkus dalam payload `{ type: 'CONTEXT_UPDATE', payload: ... }` dan disiarkan ke semua klien yang terhubung.

7.  **Pembaruan Konteks Frontend (`DashboardContext.tsx`)**: Hook `useWebSocket` di `DashboardProvider` menerima pesan tersebut. Ia memanggil fungsi `setContext`, yang memperbarui state global.

8.  **Render Ulang UI (`dashboard/page.tsx`)**: Perubahan state ini memicu React untuk me-render ulang komponen dasbor dengan data yang baru, menyelesaikan siklus pembaruan.

---

## 5. Aktivitas Pengguna: Implisit vs. Eksplisit

*   **Aktivitas Implisit (Saat Ini)**: Sebagian besar interaksi pengguna bersifat implisit.
    *   **Mengamati**: Melihat perubahan konteks dan wawasan secara real-time.
    *   **Memahami**: Menggunakan informasi yang disajikan untuk membuat keputusan di dunia nyata (misalnya, memutuskan untuk memesan stok atau menjadwalkan lebih banyak staf).

*   **Aktivitas Eksplisit (Masa Depan)**: Alur saat ini menjadi fondasi untuk tindakan eksplisit di masa depan.
    *   **Menindaklanjuti Wawasan**: Mengklik tombol "Tindakan" pada `InsightCard` untuk langsung melakukan sesuatu (misalnya, membuka halaman pemesanan inventaris).
    *   **Memfilter dan Mencari**: Menggunakan kontrol UI untuk memfilter transaksi atau mencari pelanggan tertentu.
    *   **Mengekspor Laporan**: Mengunduh data dalam format CSV untuk analisis lebih lanjut.

Dengan mendokumentasikan alur ini, kita dapat memastikan bahwa setiap fitur baru yang dikembangkan di masa depan selaras dengan filosofi inti aplikasi untuk memberdayakan pengguna melalui kecerdasan proaktif.
