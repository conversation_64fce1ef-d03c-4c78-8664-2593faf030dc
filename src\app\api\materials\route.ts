import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, MaterialCategory } from '@/generated/prisma';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schema for material creation/update
const materialSchema = z.object({
  material_name: z.string().min(1, 'Material name is required').max(100, 'Name too long'),
  current_stock_unit: z.number().min(0, 'Stock cannot be negative'),
  unit_of_measure: z.string().min(1, 'Unit of measure is required'),
  usage_rate_per_transaction: z.number().min(0, 'Usage rate cannot be negative').optional(),
  usage_rate_per_kg: z.number().min(0, 'Usage rate cannot be negative').optional(),
  minimum_stock_threshold: z.number().min(0, 'Threshold cannot be negative').optional(),
  cost_per_unit: z.number().min(0, 'Cost cannot be negative').optional(),
  supplier_info: z.string().max(200, 'Supplier info too long').optional().nullable(),
  category: z.nativeEnum(MaterialCategory),
});

// GET /api/materials - Get all materials or by query
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const lowStock = searchParams.get('lowStock') === 'true';
    const sortBy = searchParams.get('sortBy') || 'material_name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { material_name: { contains: search } },
        { supplier_info: { contains: search } },
      ];
    }

    if (category && Object.values(MaterialCategory).includes(category as MaterialCategory)) {
      where.category = category;
    }

    if (lowStock) {
      where.current_stock_unit = {
        lte: prisma.materialInventory.fields.minimum_stock_threshold
      };
    }

    // Build orderBy clause
    const orderBy = { [sortBy]: sortOrder };

    const [materials, total] = await Promise.all([
      prisma.materialInventory.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          _count: {
            select: { transaction_materials: true }
          }
        }
      }),
      prisma.materialInventory.count({ where })
    ]);

    // Add stock status to each material
    const materialsWithStatus = materials.map(material => ({
      ...material,
      stock_status: material.current_stock_unit <= material.minimum_stock_threshold
        ? 'LOW'
        : material.current_stock_unit <= material.minimum_stock_threshold * 2
        ? 'MEDIUM'
        : 'HIGH'
    }));

    return NextResponse.json({
      success: true,
      data: materialsWithStatus,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching materials:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch materials',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/materials - Create new material
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input data
    const validatedData = materialSchema.parse(body);

    // Check if material name already exists
    const existingMaterial = await prisma.materialInventory.findUnique({
      where: { material_name: validatedData.material_name }
    });

    if (existingMaterial) {
      return NextResponse.json(
        {
          success: false,
          error: 'Material with this name already exists'
        },
        { status: 400 }
      );
    }

    const material = await prisma.materialInventory.create({
      data: validatedData,
      include: {
        _count: {
          select: { transaction_materials: true }
        }
      }
    });

    return NextResponse.json(
      {
        success: true,
        data: material,
        message: 'Material created successfully'
      },
      { status: 201 }
    );

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error creating material:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create material',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PATCH /api/materials/restock - Bulk restock materials
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();

    const restockSchema = z.object({
      materials: z.array(z.object({
        id: z.string(),
        additional_stock: z.number().min(0, 'Additional stock cannot be negative'),
        cost_per_unit: z.number().min(0, 'Cost cannot be negative').optional(),
      }))
    });

    const { materials } = restockSchema.parse(body);

    const updatePromises = materials.map(async (item) => {
      const currentMaterial = await prisma.materialInventory.findUnique({
        where: { id: item.id }
      });

      if (!currentMaterial) {
        throw new Error(`Material with ID ${item.id} not found`);
      }

      return prisma.materialInventory.update({
        where: { id: item.id },
        data: {
          current_stock_unit: currentMaterial.current_stock_unit + item.additional_stock,
          last_restock_date: new Date(),
          ...(item.cost_per_unit && { cost_per_unit: item.cost_per_unit })
        }
      });
    });

    const updatedMaterials = await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      data: updatedMaterials,
      message: `Successfully restocked ${materials.length} materials`
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error restocking materials:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to restock materials',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
