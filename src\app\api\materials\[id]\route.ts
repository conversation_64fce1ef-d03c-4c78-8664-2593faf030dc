import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, MaterialCategory } from '@/generated/prisma';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schema for material update
const materialUpdateSchema = z.object({
  material_name: z.string().min(1, 'Material name is required').max(100, 'Name too long').optional(),
  current_stock_unit: z.number().min(0, 'Stock cannot be negative').optional(),
  unit_of_measure: z.string().min(1, 'Unit of measure is required').optional(),
  usage_rate_per_transaction: z.number().min(0, 'Usage rate cannot be negative').optional(),
  usage_rate_per_kg: z.number().min(0, 'Usage rate cannot be negative').optional(),
  minimum_stock_threshold: z.number().min(0, 'Threshold cannot be negative').optional(),
  cost_per_unit: z.number().min(0, 'Cost cannot be negative').optional(),
  supplier_info: z.string().max(200, 'Supplier info too long').optional().nullable(),
  category: z.nativeEnum(MaterialCategory).optional(),
});

// GET /api/materials/[id] - Get material by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const material = await prisma.materialInventory.findUnique({
      where: { id },
      include: {
        transaction_materials: {
          orderBy: { transaction: { transaction_date: 'desc' } },
          take: 10, // Last 10 usage records
          include: {
            transaction: {
              select: {
                id: true,
                transaction_date: true,
                service_type: true,
                weight_kg: true,
                customer: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        },
        _count: {
          select: { transaction_materials: true }
        }
      }
    });

    if (!material) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Material not found' 
        },
        { status: 404 }
      );
    }

    // Calculate usage statistics
    const totalUsage = material.transaction_materials.reduce(
      (sum, tm) => sum + tm.quantity_used, 0
    );

    const materialWithStats = {
      ...material,
      stock_status: material.current_stock_unit <= material.minimum_stock_threshold 
        ? 'LOW' 
        : material.current_stock_unit <= material.minimum_stock_threshold * 2 
        ? 'MEDIUM' 
        : 'HIGH',
      usage_stats: {
        total_usage: totalUsage,
        usage_count: material._count.transaction_materials,
        average_usage_per_transaction: material._count.transaction_materials > 0 
          ? totalUsage / material._count.transaction_materials 
          : 0
      }
    };

    return NextResponse.json({
      success: true,
      data: materialWithStats
    });

  } catch (error) {
    console.error('Error fetching material:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch material',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/materials/[id] - Update material
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    // Validate input data
    const validatedData = materialUpdateSchema.parse(body);

    // Check if material exists
    const existingMaterial = await prisma.materialInventory.findUnique({
      where: { id }
    });

    if (!existingMaterial) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Material not found' 
        },
        { status: 404 }
      );
    }

    // Check for unique constraint if updating material name
    if (validatedData.material_name && validatedData.material_name !== existingMaterial.material_name) {
      const nameExists = await prisma.materialInventory.findUnique({
        where: { material_name: validatedData.material_name }
      });

      if (nameExists) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Material name already exists' 
          },
          { status: 400 }
        );
      }
    }

    const updatedMaterial = await prisma.materialInventory.update({
      where: { id },
      data: validatedData,
      include: {
        _count: {
          select: { transaction_materials: true }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedMaterial,
      message: 'Material updated successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error updating material:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update material',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/materials/[id] - Delete material
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if material exists
    const existingMaterial = await prisma.materialInventory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { transaction_materials: true }
        }
      }
    });

    if (!existingMaterial) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Material not found' 
        },
        { status: 404 }
      );
    }

    // Check if material has been used in transactions
    if (existingMaterial._count.transaction_materials > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot delete material that has been used in transactions. Consider archiving instead.' 
        },
        { status: 400 }
      );
    }

    await prisma.materialInventory.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Material deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting material:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete material',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PATCH /api/materials/[id]/restock - Restock specific material
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    const restockSchema = z.object({
      additional_stock: z.number().min(0, 'Additional stock cannot be negative'),
      cost_per_unit: z.number().min(0, 'Cost cannot be negative').optional(),
    });

    const { additional_stock, cost_per_unit } = restockSchema.parse(body);

    const currentMaterial = await prisma.materialInventory.findUnique({
      where: { id }
    });

    if (!currentMaterial) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Material not found' 
        },
        { status: 404 }
      );
    }

    const updatedMaterial = await prisma.materialInventory.update({
      where: { id },
      data: {
        current_stock_unit: currentMaterial.current_stock_unit + additional_stock,
        last_restock_date: new Date(),
        ...(cost_per_unit && { cost_per_unit })
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedMaterial,
      message: `Successfully restocked ${additional_stock} ${currentMaterial.unit_of_measure} of ${currentMaterial.material_name}`
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error restocking material:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to restock material',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
