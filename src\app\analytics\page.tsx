"use client";

import React, { useState, useEffect } from 'react';
import { useDashboard } from '../context/DashboardContext';
import { BarChart3, TrendingUp, Users, Package, DollarSign, Calendar } from 'lucide-react';

interface AnalyticsData {
  revenue: {
    daily: Array<{ date: string; amount: number }>;
    monthly: Array<{ month: string; amount: number }>;
    growth: number;
  };
  transactions: {
    daily: Array<{ date: string; count: number }>;
    byService: Array<{ service: string; count: number; percentage: number }>;
    avgValue: number;
  };
  customers: {
    new: Array<{ date: string; count: number }>;
    returning: Array<{ date: string; count: number }>;
    retention: number;
  };
  materials: {
    usage: Array<{ material: string; used: number; cost: number }>;
    efficiency: number;
  };
}

const AnalyticsPage: React.FC = () => {
  const { patternData, realTimeKpis } = useDashboard();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/analytics?range=${timeRange}`);
        if (response.ok) {
          const data = await response.json();
          setAnalyticsData(data);
        }
      } catch (error) {
        console.error('Failed to fetch analytics data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Memuat data analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
        <div className="flex gap-2">
          {(['7d', '30d', '90d'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeRange === range
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {range === '7d' ? '7 Hari' : range === '30d' ? '30 Hari' : '90 Hari'}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analyticsData?.revenue.monthly.reduce((sum, item) => sum + item.amount, 0) || 0)}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${
              (analyticsData?.revenue.growth || 0) >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatPercentage(analyticsData?.revenue.growth || 0)}
            </span>
            <span className="text-sm text-gray-500 ml-1">vs periode sebelumnya</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rata-rata Transaksi</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analyticsData?.transactions.avgValue || 0)}
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <BarChart3 className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              Total {analyticsData?.transactions.daily.reduce((sum, item) => sum + item.count, 0) || 0} transaksi
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Customer Retention</p>
              <p className="text-2xl font-bold text-gray-900">
                {(analyticsData?.customers.retention || 0).toFixed(1)}%
              </p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              Pelanggan yang kembali
            </span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Material Efficiency</p>
              <p className="text-2xl font-bold text-gray-900">
                {(analyticsData?.materials.efficiency || 0).toFixed(1)}%
              </p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <Package className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">
              Efisiensi penggunaan material
            </span>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
          <div className="h-64 flex items-end justify-between space-x-2">
            {analyticsData?.revenue.daily.slice(-7).map((item, index) => {
              const maxAmount = Math.max(...(analyticsData?.revenue.daily.map(d => d.amount) || [1]));
              const height = (item.amount / maxAmount) * 100;
              return (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="bg-blue-500 rounded-t w-full min-h-[4px]"
                    style={{ height: `${height}%` }}
                    title={formatCurrency(item.amount)}
                  />
                  <span className="text-xs text-gray-500 mt-2">
                    {new Date(item.date).toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit' })}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Service Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribusi Layanan</h3>
          <div className="space-y-3">
            {analyticsData?.transactions.byService.map((service, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{service.service}</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${service.percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-500 w-12 text-right">
                    {service.percentage.toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Material Usage */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Penggunaan Material</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 text-sm font-medium text-gray-600">Material</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">Jumlah Terpakai</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">Biaya</th>
                <th className="text-right py-2 text-sm font-medium text-gray-600">% dari Total</th>
              </tr>
            </thead>
            <tbody>
              {analyticsData?.materials.usage.map((material, index) => {
                const totalCost = analyticsData.materials.usage.reduce((sum, item) => sum + item.cost, 0);
                const percentage = totalCost > 0 ? (material.cost / totalCost) * 100 : 0;
                return (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-3 text-sm text-gray-900">{material.material}</td>
                    <td className="py-3 text-sm text-gray-600 text-right">{material.used}</td>
                    <td className="py-3 text-sm text-gray-600 text-right">{formatCurrency(material.cost)}</td>
                    <td className="py-3 text-sm text-gray-600 text-right">{percentage.toFixed(1)}%</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pattern Analysis Summary */}
      {patternData && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Pattern Analysis Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {patternData.customerPatterns?.length || 0}
              </p>
              <p className="text-sm text-gray-600">Customer Patterns</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {patternData.materialPatterns?.length || 0}
              </p>
              <p className="text-sm text-gray-600">Material Patterns</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {patternData.revenuePatterns?.length || 0}
              </p>
              <p className="text-sm text-gray-600">Revenue Patterns</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
