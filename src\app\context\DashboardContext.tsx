"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, ElementType } from 'react';
import { Zap, Eye, Calendar, TrendingUp, DollarSign, ShoppingCart, Users } from 'lucide-react';

// --- TYPE DEFINITIONS ---
import {
  GeneratedInsight,
  InsightGenerationInput,
  RealTimeData,
  OverduePickupItem
} from '../../lib/contextual-intelligence/types/insight-types';
import { 
  type PatternData, 
  type CurrentContextObject,
  type ContextMode as AppContextMode 
} from '../../lib/contextual-intelligence/types';
import { SmartInsightGenerator } from '../../lib/contextual-intelligence/engines/smart-insight-generator';

export type ContextMode = 'morning' | 'midday' | 'evening' | 'planning';

export interface ContextConfig {
  title: string;
  subtitle: string;
  color: string;
  Icon: ElementType;
}

export interface Kpi {
  id: string;
  label: string;
  value: string;
  icon: React.ElementType;
  trend: string;
  color: string;
}

export interface LowStockMaterial {
  id: string;
  name: string;
  currentStock: number;
  minStock: number;
  unit: string;
}

export interface OverduePickupAPIResponseItem {
  transaction_id: string;
  customer_name: string;
  customer_phone: string;
  service_name: string;
  total_weight: number;
  pickup_date: string;
  days_overdue: number;
}

interface DashboardContextType {
  currentTime: Date;
  contextMode: ContextMode;
  setContextMode: React.Dispatch<React.SetStateAction<ContextMode>>;
  currentConfig: ContextConfig;
  insights: GeneratedInsight[];
  contextConfig: Record<ContextMode, ContextConfig>;
  kpis: Kpi[];
  lowStockAlerts: LowStockMaterial[];
  overduePickupAlerts: OverduePickupAPIResponseItem[];
  realTimeKpis: Partial<RealTimeData>;
  patternData: PatternData | null;
}

const contextConfig: Record<ContextMode, ContextConfig> = {
  morning: {
    title: "Siap Operasional",
    subtitle: "3 hal yang perlu dihandle hari ini",
    color: "from-amber-500 to-orange-500",
    Icon: Zap
  },
  midday: {
    title: "Monitoring Mode",
    subtitle: "Semua berjalan normal",
    color: "from-blue-500 to-cyan-500",
    Icon: Eye
  },
  evening: {
    title: "Review & Planning",
    subtitle: "Hari ini & persiapan besok",
    color: "from-purple-500 to-pink-500",
    Icon: Calendar
  },
  planning: {
    title: "Strategic Mode",
    subtitle: "Waktu untuk planning jangka panjang",
    color: "from-green-500 to-teal-500",
    Icon: TrendingUp
  }
};

const mockKpis: Kpi[] = [
  {
    id: 'revenue',
    label: 'Pendapatan Hari Ini',
    value: 'Rp 0',
    icon: DollarSign,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
  {
    id: 'orders',
    label: 'Pesanan Aktif',
    value: '0',
    icon: ShoppingCart,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
  {
    id: 'customers',
    label: 'Pelanggan Baru',
    value: '0',
    icon: Users,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
  {
    id: 'alerts',
    label: 'Mesin Siaga',
    value: '0 / 0',
    icon: Zap,
    trend: 'Memuat...',
    color: 'text-gray-500',
  },
];

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [contextMode, setContextMode] = useState<ContextMode>('morning');
  const [kpis, setKpis] = useState<Kpi[]>(mockKpis);
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockMaterial[]>([]);
  const [overduePickupAlerts, setOverduePickupAlerts] = useState<OverduePickupAPIResponseItem[]>([]);
  const [insights, setInsights] = useState<GeneratedInsight[]>([]);
  const [realTimeKpis, setRealTimeKpis] = useState<Partial<RealTimeData>>({});
  const [patternData, setPatternData] = useState<PatternData | null>(null);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const hour = currentTime.getHours();
    if (hour >= 7 && hour < 10) setContextMode('morning');
    else if (hour >= 10 && hour < 16) setContextMode('midday');
    else if (hour >= 17 && hour < 20) setContextMode('evening');
    else setContextMode('planning');
  }, [currentTime]);

  useEffect(() => {
    const fetchInventoryStatus = async () => {
      try {
        const response = await fetch('/api/inventory/low-stock');
        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
        const data: LowStockMaterial[] = await response.json();
        setLowStockAlerts(data);
      } catch (error) {
        console.error("Failed to fetch inventory status:", error);
        setLowStockAlerts([]);
      }
    };
    fetchInventoryStatus();
  }, []);

  useEffect(() => {
    const fetchOverduePickups = async () => {
      try {
        const response = await fetch('/api/transactions/overdue-pickups');
        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
        const data: OverduePickupAPIResponseItem[] = await response.json();
        setOverduePickupAlerts(data);
      } catch (error) {
        console.error("Failed to fetch overdue pickups:", error);
        setOverduePickupAlerts([]);
      }
    };
    fetchOverduePickups();
  }, []);

  // --- REAL-TIME DATA WITH WEBSOCKET ---
  useEffect(() => {
    // Use window.location.hostname to connect to the same host the page is served from
    const ws = new WebSocket(`ws://${window.location.hostname}:3000`);

    ws.onopen = () => {
      console.log('[WebSocket] Connection established');
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        // We handle two types of messages: the initial full state, and subsequent updates
        if (message.type === 'INITIAL_STATE' || message.type === 'DASHBOARD_STATE_UPDATE') {
          console.log(`[WebSocket] Received ${message.type}`, message.payload);
          const { kpis, patterns } = message.payload;
          
          if (kpis) {
            setRealTimeKpis(kpis);
          }
          if (patterns) {
            setPatternData(patterns);
          }
        }
      } catch (error) {
        console.error('[WebSocket] Error processing message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('[WebSocket] Error:', error);
    };

    ws.onclose = () => {
      console.log('[WebSocket] Connection closed');
    };

    // Cleanup function to close the connection when the component unmounts
    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, []); // Empty dependency array ensures this effect runs only once on mount

  useEffect(() => {
    if (realTimeKpis.transactions || realTimeKpis.operations || realTimeKpis.customers) {
      const updatedKpis: Kpi[] = [
        {
          id: 'revenue',
          label: 'Pendapatan Hari Ini',
          value: `Rp ${realTimeKpis.transactions?.today_revenue?.toLocaleString('id-ID') || '0'}`,
          icon: DollarSign,
          trend: `${realTimeKpis.transactions?.growth_percentage_vs_yesterday !== undefined ? (realTimeKpis.transactions.growth_percentage_vs_yesterday >= 0 ? '+' : '') + realTimeKpis.transactions.growth_percentage_vs_yesterday.toFixed(1) + '%' : ''} vs kemarin`,
          color: realTimeKpis.transactions?.growth_percentage_vs_yesterday !== undefined ? (realTimeKpis.transactions.growth_percentage_vs_yesterday >= 0 ? 'text-green-500' : 'text-red-500') : 'text-gray-500',
        },
        {
          id: 'orders',
          label: 'Pesanan Aktif',
          value: `${realTimeKpis.operations?.queue_length || '0'}`,
          icon: ShoppingCart,
          trend: `${realTimeKpis.transactions?.new_orders_last_hour || '0'} baru jam ini`,
          color: 'text-sky-500',
        },
        {
          id: 'customers',
          label: 'Pelanggan Baru',
          value: `${realTimeKpis.customers?.new_customers_today || '0'}`,
          icon: Users,
          trend: 'Hari ini',
          color: 'text-indigo-500',
        },
        {
          id: 'alerts',
          label: 'Mesin Siaga',
          value: `${realTimeKpis.operations?.active_machines || '0'} / ${realTimeKpis.operations?.total_machines || '0'}`,
          icon: Zap,
          trend: `${realTimeKpis.operations?.total_machines ? ((realTimeKpis.operations?.active_machines || 0) / realTimeKpis.operations.total_machines * 100).toFixed(0) : '0'}% utilisasi`,
          color: 'text-amber-500',
        },
      ];
      setKpis(updatedKpis);
    }
  }, [realTimeKpis]);

  useEffect(() => {
    if (!patternData) {
      setInsights([]);
      return;
    }

    const generator = new SmartInsightGenerator();

    const getAppContextMode = (date: Date): AppContextMode => {
      const hour = date.getHours();
      if (hour >= 5 && hour < 12) return 'morning';
      if (hour >= 12 && hour < 18) return 'afternoon';
      if (hour >= 18 && hour < 22) return 'evening';
      return 'night';
    };

    const currentContextObject: CurrentContextObject = {
      time: {
        timeOfDay: getAppContextMode(currentTime),
        date: currentTime,
      },
      businessCycle: {
        status: 'normal_season', // Placeholder, to be replaced by a real detection engine
      },
      userBehavior: {
        activityLevel: 'low', // Placeholder
      },
      dataQuality: {
        completenessScore: 0.95, // Placeholder
        freshness: 'current',
      },
    };

    const mappedOverduePickupItems: OverduePickupItem[] = overduePickupAlerts.map(item => ({
      transactionId: item.transaction_id,
      customerName: item.customer_name,
      customerPhone: item.customer_phone,
      serviceType: item.service_name,
      weightKg: item.total_weight,
      pickupDate: item.pickup_date,
      daysOverdue: item.days_overdue,
    }));

    // Ensure we have a valid RealTimeData object, even if some parts are missing
    const comprehensiveRealTimeData: RealTimeData = {
      transactions: realTimeKpis.transactions || {
        today_count: 0,
        today_revenue: 0,
        current_hour_count: 0,
        average_transaction_value: 0,
        peak_hour_today: null,
      },
      materials: realTimeKpis.materials || lowStockAlerts.map(item => ({
        material_id: item.id,
        material_name: item.name,
        current_stock: item.currentStock,
        minimum_threshold: item.minStock,
        category: 'default',
        last_restock_date: new Date().toISOString(),
        usage_rate_per_day: 0, // Placeholder
        days_until_empty: 99, // Placeholder
      })),
      customers: realTimeKpis.customers || {
        new_customers_today: 0,
        returning_customers_today: 0,
        pending_orders: 0,
      },
      overduePickupItems: mappedOverduePickupItems,
      operations: realTimeKpis.operations || {
        active_machines: 0,
        total_machines: 5,
        queue_length: 0,
        estimated_completion_times: [],
        staff_on_duty: 0,
      },
    };

    const insightInput: InsightGenerationInput = {
      contextObject: currentContextObject,
      patternData: patternData,
      realTimeData: comprehensiveRealTimeData,
      timestamp: currentTime,
    };

    try {
      const result = generator.generateInsights(insightInput);
      setInsights(result.insights);
    } catch (error) {
      console.error("Error generating insights:", error);
      setInsights([]);
    }
  }, [currentTime, contextMode, lowStockAlerts, overduePickupAlerts, realTimeKpis, patternData]);

  const value = {
    currentTime,
    contextMode,
    setContextMode,
    currentConfig: contextConfig[contextMode],
    insights,
    contextConfig,
    kpis,
    lowStockAlerts,
    overduePickupAlerts,
    realTimeKpis,
    patternData,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
