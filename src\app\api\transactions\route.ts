import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, ServiceType, TransactionStatus, WeatherContext, DayType, TimeOfDay } from '@/generated/prisma';
import { z } from 'zod';
import WebSocket from 'ws';

const prisma = new PrismaClient();

// --- WebSocket Trigger Function ---
// This function will be called to notify the WebSocket server that new data is available
// and a re-analysis of the dashboard state is required.
const triggerContextualEngine = () => {
  // Fire-and-forget: We don't need to wait for the response.
  // The goal is just to send a signal.
  try {
    const ws = new WebSocket('ws://localhost:3000');

    ws.on('open', () => {
      console.log('[API] WebSocket connection opened to trigger engine.');
      ws.send(JSON.stringify({ type: 'TRIGGER_ANALYSIS', payload: { source: 'new_transaction' } }));
      // Close the connection after sending the message
      ws.close();
    });

    ws.on('close', () => {
      console.log('[API] WebSocket connection closed after triggering engine.');
    });

    ws.on('error', (error) => {
      // Log the error but don't let it crash the API response.
      // The primary operation (saving the transaction) has already succeeded.
      console.error('[API] Error connecting to WebSocket server:', error.message);
    });
  } catch (error: any) {
    console.error('[API] Failed to initiate WebSocket trigger:', error.message);
  }
};

// Validation schema for transaction creation
const transactionSchema = z.object({
  customer_id: z.string().min(1, 'Customer ID is required'),
  service_type: z.nativeEnum(ServiceType),
  weight_kg: z.number().min(0.1, 'Weight must be at least 0.1 kg'),
  price: z.number().min(0, 'Price cannot be negative'),
  context_weather: z.nativeEnum(WeatherContext).optional(),
  context_day_type: z.nativeEnum(DayType).optional(),
  context_seasonal_factor: z.number().min(0).max(1).optional(),
  context_time_of_day: z.nativeEnum(TimeOfDay).optional(),
  pickup_date: z.string().datetime().optional(),
  delivery_date: z.string().datetime().optional(),
  special_instructions: z.string().max(500, 'Instructions too long').optional(),
  discount_applied: z.number().min(0).max(100).optional(),
  materials: z.array(z.object({
    material_id: z.string(),
    quantity_used: z.number().min(0, 'Quantity cannot be negative'),
  })).optional(),
});

// GET /api/transactions - Get all transactions or by query
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const customer_id = searchParams.get('customer_id');
    const status = searchParams.get('status');
    const service_type = searchParams.get('service_type');
    const date_from = searchParams.get('date_from');
    const date_to = searchParams.get('date_to');
    const sortBy = searchParams.get('sortBy') || 'transaction_date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (customer_id) {
      where.customer_id = customer_id;
    }

    if (status && Object.values(TransactionStatus).includes(status as TransactionStatus)) {
      where.status = status;
    }

    if (service_type && Object.values(ServiceType).includes(service_type as ServiceType)) {
      where.service_type = service_type;
    }

    if (date_from || date_to) {
      where.transaction_date = {};
      if (date_from) {
        where.transaction_date.gte = new Date(date_from);
      }
      if (date_to) {
        where.transaction_date.lte = new Date(date_to);
      }
    }

    // Build orderBy clause
    const orderBy = { [sortBy]: sortOrder };

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              phone_number: true,
            }
          },
          transaction_materials: {
            include: {
              material: {
                select: {
                  id: true,
                  material_name: true,
                  unit_of_measure: true,
                }
              }
            }
          }
        }
      }),
      prisma.transaction.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch transactions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/transactions - Create new transaction
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input data
    const validatedData = transactionSchema.parse(body);

    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: validatedData.customer_id }
    });

    if (!customer) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Customer not found' 
        },
        { status: 400 }
      );
    }

    // Auto-detect context if not provided
    const now = new Date();
    const hour = now.getHours();
    const dayOfWeek = now.getDay();

    const contextData = {
      context_weather: validatedData.context_weather || WeatherContext.SUNNY,
      context_day_type: validatedData.context_day_type || 
        (dayOfWeek === 0 || dayOfWeek === 6 ? DayType.WEEKEND : DayType.WEEKDAY),
      context_seasonal_factor: validatedData.context_seasonal_factor || 0.5,
      context_time_of_day: validatedData.context_time_of_day || 
        (hour < 9 ? TimeOfDay.EARLY_MORNING :
         hour < 12 ? TimeOfDay.MORNING :
         hour < 17 ? TimeOfDay.AFTERNOON :
         hour < 20 ? TimeOfDay.EVENING : TimeOfDay.NIGHT),
    };

    // Start transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create the transaction
      const transaction = await tx.transaction.create({
        data: {
          customer_id: validatedData.customer_id,
          service_type: validatedData.service_type,
          weight_kg: validatedData.weight_kg,
          price: validatedData.price,
          pickup_date: validatedData.pickup_date ? new Date(validatedData.pickup_date) : null,
          delivery_date: validatedData.delivery_date ? new Date(validatedData.delivery_date) : null,
          special_instructions: validatedData.special_instructions,
          discount_applied: validatedData.discount_applied || 0,
          ...contextData,
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              phone_number: true,
            }
          }
        }
      });

      // Handle materials if provided
      if (validatedData.materials && validatedData.materials.length > 0) {
        for (const materialUsage of validatedData.materials) {
          // Check if material exists and has sufficient stock
          const material = await tx.materialInventory.findUnique({
            where: { id: materialUsage.material_id }
          });

          if (!material) {
            throw new Error(`Material with ID ${materialUsage.material_id} not found`);
          }

          if (material.current_stock_unit < materialUsage.quantity_used) {
            throw new Error(`Insufficient stock for ${material.material_name}. Available: ${material.current_stock_unit}, Required: ${materialUsage.quantity_used}`);
          }

          // Create transaction material record
          await tx.transactionMaterial.create({
            data: {
              transaction_id: transaction.id,
              material_id: materialUsage.material_id,
              quantity_used: materialUsage.quantity_used,
              cost_at_time: material.cost_per_unit * materialUsage.quantity_used,
            }
          });

          // Update material stock
          await tx.materialInventory.update({
            where: { id: materialUsage.material_id },
            data: {
              current_stock_unit: material.current_stock_unit - materialUsage.quantity_used,
            }
          });
        }
      }

      // Update customer's last transaction date
      await tx.customer.update({
        where: { id: validatedData.customer_id },
        data: {
          last_transaction_date: transaction.transaction_date,
        }
      });

      return transaction;
    });

    // The transaction was successful

    // --- CRUCIAL STEP: Trigger the contextual engine ---
    // This happens asynchronously and does not block the API response.
    triggerContextualEngine();

    return NextResponse.json(
      { 
        success: true, 
        data: result,
        message: 'Transaction created successfully'
      },
      { status: 201 }
    );

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error creating transaction:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create transaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
